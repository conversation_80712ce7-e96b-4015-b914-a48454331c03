import js from '@eslint/js';
import globals from 'globals';
import reactHooks from 'eslint-plugin-react-hooks';
import reactRefresh from 'eslint-plugin-react-refresh';
import tseslint from 'typescript-eslint';
import prettier from 'eslint-plugin-prettier';
import prettierConfig from 'eslint-config-prettier';
// Note: The following plugins need to be installed:
// npm install --save-dev eslint-plugin-react eslint-plugin-jsx-a11y eslint-plugin-import

export default tseslint.config([
  // Global ignores
  {
    ignores: ['dist/**', 'build/**', 'vite.config.mjs']
  },

  // Main configuration for TypeScript and React files
  {
    files: ['**/*.{js,jsx,ts,tsx}'],
    extends: [
      js.configs.recommended,
      ...tseslint.configs.recommended,
      prettierConfig
    ],
    plugins: {
      'react-hooks': reactHooks,
      'react-refresh': reactRefresh,
      prettier
      // Note: Add these plugins after installing the dependencies:
      // react,
      // 'jsx-a11y': jsxA11y,
      // import: importPlugin,
    },
    languageOptions: {
      ecmaVersion: 2021,
      globals: {
        ...globals.browser,
        ...globals.es2021,
        NEXT_PUBLIC_BACKEND_URL: 'readonly',
        REACT_APP_BACKEND_URL: 'readonly',
        COPYRIGHT_YEAR: 'readonly'
      },
      parserOptions: {
        ecmaFeatures: {
          jsx: true
        },
        projectService: true,
        tsconfigRootDir: import.meta.dirname
      }
    },
    settings: {
      // Note: Add these settings after installing eslint-plugin-react and eslint-plugin-import:
      // react: {
      //   version: 'detect'
      // },
      // 'import/resolver': {
      //   typescript: {
      //     alwaysTryTypes: true
      //   }
      // }
    },
    rules: {
      // React Hooks rules
      ...reactHooks.configs.recommended.rules,

      // React Refresh rules
      'react-refresh/only-export-components': [
        'warn',
        { allowConstantExport: true }
      ],

      // Prettier rules
      'prettier/prettier': 'error'

      // Custom rules from original .eslintrc.js
      // Note: Uncomment these rules after installing the required plugins:
      // 'import/no-extraneous-dependencies': 'off',
      // 'import/prefer-default-export': 'off',
      // 'jsx-a11y/label-has-for': 'off',
      // 'react/prop-types': 'off',
      // 'react/react-in-jsx-scope': 'off',
      // 'react/function-component-definition': [
      //   'error',
      //   {
      //     namedComponents: 'arrow-function',
      //     unnamedComponents: 'arrow-function'
      //   }
      // ],
      // 'react/require-default-props': 'off',
      // 'react/jsx-props-no-spreading': 'off'
    }
  },

  // Configuration for config files
  {
    files: ['eslint.config.js', '.eslintrc.{js,cjs}'],
    languageOptions: {
      globals: {
        ...globals.node
      },
      parserOptions: {
        sourceType: 'script'
      }
    }
  }
]);
